import React, { useState } from 'react';
import { Button, Table, Modal, Input, Form, message } from 'antd';
import { ethers } from 'ethers';

export default function Wallets() {
  const [wallets, setWallets] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);

  const createWallet = async () => {
    const wallet = ethers.Wallet.createRandom();
    setWallets([...wallets, {
      address: wallet.address,
      privateKey: wallet.privateKey,
    }]);
    message.success('钱包创建成功');
  };

  return (
    <div>
      <Button type="primary" onClick={createWallet} style={{marginBottom:10}}>创建新钱包</Button>
      <Table
        dataSource={wallets}
        rowKey="address"
        columns={[
          { title: '地址', dataIndex: 'address' },
          { title: '私钥', dataIndex: 'privateKey' },
        ]}
      />
    </div>
  );
}
