import React from 'react';
import { Outlet } from 'umi';
import { Layout, Menu } from 'antd';
import {
  UserOutlined,
  WalletOutlined,
  SettingOutlined,
  FileTextOutlined,
} from '@ant-design/icons';

const { Header, Content, Sider } = Layout;

export default function BasicLayout() {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider>
        <div style={{ color: '#fff', padding: 16, fontSize: 18 }}>Web3后台</div>
        <Menu theme="dark" mode="inline">
          {/* <Menu.Item key="users" icon={<UserOutlined />}>
            <a href="/users">用户管理</a>
          </Menu.Item> */}
          <Menu.Item key="wallets" icon={<WalletOutlined />}>
            <a href="/wallets">个人钱包管理</a>
          </Menu.Item>
          <Menu.Item key="fundwallet" icon={<WalletOutlined />}>
            <a href="/fundwallet">资金归集钱包</a>
          </Menu.Item>
          {/* <Menu.Item key="params" icon={<SettingOutlined />}>
            <a href="/params">参数管理</a>
          </Menu.Item>
          <Menu.Item key="reports" icon={<FileTextOutlined />}>
            <a href="/reports">报文管理</a>
          </Menu.Item> */}
        </Menu>
      </Sider>
      <Layout>
        <Header style={{ background: '#fff', paddingLeft: 20 }}>Web3后台管理</Header>
        <Content style={{ margin: '16px' }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}
