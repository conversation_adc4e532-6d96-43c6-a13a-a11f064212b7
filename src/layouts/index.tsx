import React from 'react';
import { Outlet, Link, useLocation } from 'umi';
import { Layout, Menu } from 'antd';
import {
  UserOutlined,
  WalletOutlined,
  SettingOutlined,
  FileTextOutlined,
} from '@ant-design/icons';

const { Header, Content, Sider } = Layout;

export default function BasicLayout() {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider>
        <div style={{ color: '#fff', padding: 16, fontSize: 18 }}>Web3后台</div>
        <Menu theme="dark" mode="inline" selectedKeys={[currentPath]}>
          <Menu.Item key="/wallets" icon={<WalletOutlined />}>
            <Link to="/wallets">个人钱包管理</Link>
          </Menu.Item>
          <Menu.Item key="/fundwallet" icon={<WalletOutlined />}>
            <Link to="/fundwallet">资金归集钱包</Link>
          </Menu.Item>
        </Menu>
      </Sider>
      <Layout>
        <Header style={{ background: '#fff', paddingLeft: 20 }}>Web3后台管理</Header>
        <Content style={{ margin: '16px' }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}
