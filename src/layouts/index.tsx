import React from 'react';
import { Outlet, Link, useLocation } from 'umi';
import { Layout, Menu } from 'antd';
import { WalletOutlined, DashboardOutlined } from '@ant-design/icons';

const { Header, Content, Sider } = Layout;

export default function BasicLayout() {
  const location = useLocation();
  const currentPath = location.pathname;

  const menuItems = [
    {
      key: '/wallets',
      icon: <WalletOutlined />,
      label: <Link to="/wallets">个人钱包管理</Link>,
    },
    {
      key: '/fundwallet',
      icon: <DashboardOutlined />,
      label: <Link to="/fundwallet">资金归集钱包</Link>,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={200}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div
          style={{
            color: '#fff',
            padding: '16px',
            fontSize: '18px',
            fontWeight: 'bold',
            textAlign: 'center',
            borderBottom: '1px solid #303030'
          }}
        >
          Web3后台
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[currentPath]}
          items={menuItems}
          style={{ borderRight: 0 }}
        />
      </Sider>
      <Layout style={{ marginLeft: 200 }}>
        <Header
          style={{
            background: '#fff',
            paddingLeft: '24px',
            boxShadow: '0 1px 4px rgba(0,21,41,.08)',
            position: 'sticky',
            top: 0,
            zIndex: 1,
          }}
        >
          <h2 style={{ margin: 0, color: '#001529' }}>Web3后台管理系统</h2>
        </Header>
        <Content
          style={{
            margin: '24px',
            padding: '24px',
            background: '#fff',
            borderRadius: '6px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
            minHeight: 'calc(100vh - 112px)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}
