import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: false, // 禁用内置布局插件
  routes: [
   { path: '/', component: '@/layouts/index', routes: [
    { path: '/', redirect: '/wallets' },
    { path: '/wallets', component: '@/pages/wallets/index' },
    { path: '/fundwallet', component: '@/pages/fundwallet/index' },
  ]}
  ],
  npmClient: 'npm',
});

